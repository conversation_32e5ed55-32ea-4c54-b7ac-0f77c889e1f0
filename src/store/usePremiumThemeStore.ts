import { create } from 'zustand';
import { PremiumState } from '../types/premium';
import { PremiumStorage } from '../utils/premiumStorage';

interface PremiumThemeState extends PremiumState {
  actions: {
    unlockTheme: (themeId: string) => void;
    setCurrentTheme: (themeId: string) => void;
    loadUnlockedThemes: () => void;
    saveUnlockedThemes: () => void;
  };
}

export const usePremiumThemeStore = create<PremiumThemeState>((set, get) => ({
  unlockedThemes: [],
  currentTheme: null,

  actions: {
    unlockTheme: (themeId: string) => {
      console.log('Unlocking theme:', themeId);
      set(state => {
        const newUnlockedThemes = [...state.unlockedThemes];
        if (!newUnlockedThemes.includes(themeId)) {
          newUnlockedThemes.push(themeId);
        }
        console.log('New unlocked themes:', newUnlockedThemes);
        return { unlockedThemes: newUnlockedThemes };
      });
      get().actions.saveUnlockedThemes();
    },

    setCurrentTheme: (themeId: string) => {
      console.log('Setting current theme:', themeId);
      set({ currentTheme: themeId });
      get().actions.saveUnlockedThemes();
    },

    loadUnlockedThemes: () => {
      try {
        const savedData = PremiumStorage.getUnlockedThemes();
        if (savedData) {
          set({
            unlockedThemes: savedData.unlockedThemes || [],
            currentTheme: savedData.currentTheme || null,
          });
        }
      } catch (error) {
        console.error('Failed to load unlocked themes:', error);
      }
    },

    saveUnlockedThemes: () => {
      try {
        const { unlockedThemes, currentTheme } = get();
        PremiumStorage.saveUnlockedThemes({
          unlockedThemes,
          currentTheme,
        });
      } catch (error) {
        console.error('Failed to save unlocked themes:', error);
      }
    },
  },
}));
