export interface PremiumTheme {
  id: string;
  name: string;
  description: string;
  cost: number;
  colors: {
    primary: string;
    background: string;
    card: string;
    text: string;
    textSecondary: string;
    border: string;
    surface: string;
  };
}

// Premium themes configuration
export const PREMIUM_THEMES: Record<string, PremiumTheme> = {
  OCEAN_BLUE: {
    id: 'ocean_blue',
    name: 'Ocean Blue',
    description: 'Calming blue ocean theme',
    cost: 1,
    colors: {
      primary: '#0077BE',
      background: '#F0F8FF',
      card: '#E6F3FF',
      text: '#1A1A1A',
      textSecondary: '#4A90A4',
      border: '#B3D9FF',
      surface: '#E6F3FF',
    },
  },
  FOREST_GREEN: {
    id: 'forest_green',
    name: 'Forest Green',
    description: 'Natural forest green theme',
    cost: 1,
    colors: {
      primary: '#228B22',
      background: '#F0FFF0',
      card: '#E8F5E8',
      text: '#1A1A1A',
      textSecondary: '#4A7C59',
      border: '#B3E5B3',
      surface: '#E8F5E8',
    },
  },
  SUNSET_ORANGE: {
    id: 'sunset_orange',
    name: 'Sunset Orange',
    description: 'Warm sunset orange theme',
    cost: 1,
    colors: {
      primary: '#FF6B35',
      background: '#FFF8F0',
      card: '#FFE8D6',
      text: '#1A1A1A',
      textSecondary: '#B8572A',
      border: '#FFD4B3',
      surface: '#FFE8D6',
    },
  },
  PURPLE_DREAM: {
    id: 'purple_dream',
    name: 'Purple Dream',
    description: 'Elegant purple theme',
    cost: 1,
    colors: {
      primary: '#8A2BE2',
      background: '#F8F0FF',
      card: '#F0E6FF',
      text: '#1A1A1A',
      textSecondary: '#6A4C93',
      border: '#D4B3FF',
      surface: '#F0E6FF',
    },
  },
  ROSE_GOLD: {
    id: 'rose_gold',
    name: 'Rose Gold',
    description: 'Luxurious rose gold theme',
    cost: 2,
    colors: {
      primary: '#E91E63',
      background: '#FFF0F5',
      card: '#FFE4E9',
      text: '#1A1A1A',
      textSecondary: '#B8185C',
      border: '#FFB3CC',
      surface: '#FFE4E9',
    },
  },
};

export interface PremiumState {
  unlockedThemes: string[];
  currentTheme: string | null;
}
