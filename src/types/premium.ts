// Premium features that can be unlocked with coins
export interface PremiumFeature {
  id: string;
  name: string;
  description: string;
  cost: number; // Cost in coins
  icon: string;
  isUnlocked: boolean;
}

export interface PremiumTheme {
  id: string;
  name: string;
  description: string;
  cost: number;
  colors: {
    primary: string;
    background: string;
    card: string;
    text: string;
    textSecondary: string;
    border: string;
    surface: string;
  };
  isUnlocked: boolean;
}

// Premium features configuration
export const PREMIUM_FEATURES: Record<string, Omit<PremiumFeature, 'isUnlocked'>> = {
  ADVANCED_STATS: {
    id: 'advanced_stats',
    name: 'Advanced Statistics',
    description: 'Detailed progress charts and analytics',
    cost: 5,
    icon: 'analytics',
  },
  CUSTOM_THEMES: {
    id: 'custom_themes',
    name: 'Premium Themes',
    description: 'Access to 8 beautiful premium themes',
    cost: 3,
    icon: 'palette',
  },
  HABIT_BACKUP: {
    id: 'habit_backup',
    name: 'Data Backup',
    description: 'Export and backup your habit data',
    cost: 2,
    icon: 'backup',
  },
  UNLIMITED_HABITS: {
    id: 'unlimited_habits',
    name: 'Unlimited Habits',
    description: 'Create more than 10 habits',
    cost: 4,
    icon: 'add-circle',
  },
  CUSTOM_REMINDERS: {
    id: 'custom_reminders',
    name: 'Smart Reminders',
    description: 'Advanced notification scheduling',
    cost: 3,
    icon: 'notifications-active',
  },
};

// Premium themes configuration
export const PREMIUM_THEMES: Record<string, Omit<PremiumTheme, 'isUnlocked'>> = {
  OCEAN_BLUE: {
    id: 'ocean_blue',
    name: 'Ocean Blue',
    description: 'Calming blue ocean theme',
    cost: 1,
    colors: {
      primary: '#0077BE',
      background: '#F0F8FF',
      card: '#FFFFFF',
      text: '#1A1A1A',
      textSecondary: '#666666',
      border: '#E1E8ED',
      surface: '#F7F9FA',
    },
  },
  FOREST_GREEN: {
    id: 'forest_green',
    name: 'Forest Green',
    description: 'Natural forest green theme',
    cost: 1,
    colors: {
      primary: '#228B22',
      background: '#F0FFF0',
      card: '#FFFFFF',
      text: '#1A1A1A',
      textSecondary: '#666666',
      border: '#E1E8ED',
      surface: '#F7F9FA',
    },
  },
  SUNSET_ORANGE: {
    id: 'sunset_orange',
    name: 'Sunset Orange',
    description: 'Warm sunset orange theme',
    cost: 1,
    colors: {
      primary: '#FF6B35',
      background: '#FFF8F0',
      card: '#FFFFFF',
      text: '#1A1A1A',
      textSecondary: '#666666',
      border: '#E1E8ED',
      surface: '#F7F9FA',
    },
  },
  PURPLE_DREAM: {
    id: 'purple_dream',
    name: 'Purple Dream',
    description: 'Elegant purple theme',
    cost: 1,
    colors: {
      primary: '#8A2BE2',
      background: '#F8F0FF',
      card: '#FFFFFF',
      text: '#1A1A1A',
      textSecondary: '#666666',
      border: '#E1E8ED',
      surface: '#F7F9FA',
    },
  },
  ROSE_GOLD: {
    id: 'rose_gold',
    name: 'Rose Gold',
    description: 'Luxurious rose gold theme',
    cost: 2,
    colors: {
      primary: '#E91E63',
      background: '#FFF0F5',
      card: '#FFFFFF',
      text: '#1A1A1A',
      textSecondary: '#666666',
      border: '#E1E8ED',
      surface: '#F7F9FA',
    },
  },
};

export interface PremiumState {
  unlockedFeatures: string[];
  unlockedThemes: string[];
  currentTheme: string | null;
}
