import React, { useState } from 'react';
import {
  View,
  ScrollView,
  TouchableOpacity,
  StyleSheet,
  Alert,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { useTheme } from '../../components/ThemeProvider';
import { TextApp } from '../../components';
import { usePremiumStore } from '../../store/usePremiumStore';
import { PREMIUM_FEATURES, PREMIUM_THEMES } from '../../types/premium';
import { goBack } from '../../navigators/navigation-services';

export const PremiumFeaturesScreen: React.FC = () => {
  const { theme } = useTheme();
  const { coins, actions } = usePremiumStore();
  const [activeTab, setActiveTab] = useState<'features' | 'themes'>('features');

  const handleUnlockFeature = (featureId: string, cost: number, name: string) => {
    if (coins.amount < cost) {
      Alert.alert(
        'Insufficient Coins',
        `You need ${cost} coins to unlock ${name}. You have ${coins.amount} coins.`,
        [{ text: 'OK' }]
      );
      return;
    }

    Alert.alert(
      'Unlock Feature',
      `Unlock ${name} for ${cost} coins?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Unlock',
          onPress: () => {
            const success = actions.spendCoins(cost);
            if (success) {
              // TODO: Add feature unlock logic
              Alert.alert('Success!', `${name} has been unlocked!`);
            }
          },
        },
      ]
    );
  };

  const handleUnlockTheme = (themeId: string, cost: number, name: string) => {
    if (coins.amount < cost) {
      Alert.alert(
        'Insufficient Coins',
        `You need ${cost} coins to unlock ${name}. You have ${coins.amount} coins.`,
        [{ text: 'OK' }]
      );
      return;
    }

    Alert.alert(
      'Unlock Theme',
      `Unlock ${name} theme for ${cost} coins?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Unlock',
          onPress: () => {
            const success = actions.spendCoins(cost);
            if (success) {
              // TODO: Add theme unlock logic
              Alert.alert('Success!', `${name} theme has been unlocked!`);
            }
          },
        },
      ]
    );
  };

  const renderFeatureItem = (feature: any) => (
    <View key={feature.id} style={styles.itemCard}>
      <View style={styles.itemHeader}>
        <Icon name={feature.icon} size={32} color={theme.colors.primary} />
        <View style={styles.itemInfo}>
          <TextApp preset="txt16Bold" style={styles.itemTitle}>
            {feature.name}
          </TextApp>
          <TextApp style={styles.itemDescription}>
            {feature.description}
          </TextApp>
        </View>
        <View style={styles.costContainer}>
          <Icon name="stars" size={16} color={theme.colors.primary} />
          <TextApp preset="txt14Bold" style={styles.costText}>
            {feature.cost}
          </TextApp>
        </View>
      </View>
      <TouchableOpacity
        style={styles.unlockButton}
        onPress={() => handleUnlockFeature(feature.id, feature.cost, feature.name)}
      >
        <TextApp preset="txt14Bold" style={styles.unlockButtonText}>
          Unlock
        </TextApp>
      </TouchableOpacity>
    </View>
  );

  const renderThemeItem = (theme: any) => (
    <View key={theme.id} style={styles.itemCard}>
      <View style={styles.itemHeader}>
        <View style={[styles.themePreview, { backgroundColor: theme.colors.primary }]} />
        <View style={styles.itemInfo}>
          <TextApp preset="txt16Bold" style={styles.itemTitle}>
            {theme.name}
          </TextApp>
          <TextApp style={styles.itemDescription}>
            {theme.description}
          </TextApp>
        </View>
        <View style={styles.costContainer}>
          <Icon name="stars" size={16} color={theme.colors.primary} />
          <TextApp preset="txt14Bold" style={styles.costText}>
            {theme.cost}
          </TextApp>
        </View>
      </View>
      <TouchableOpacity
        style={styles.unlockButton}
        onPress={() => handleUnlockTheme(theme.id, theme.cost, theme.name)}
      >
        <TextApp preset="txt14Bold" style={styles.unlockButtonText}>
          Unlock
        </TextApp>
      </TouchableOpacity>
    </View>
  );

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    header: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: 20,
      paddingVertical: 16,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.border,
    },
    headerTitle: {
      color: theme.colors.text,
      marginLeft: 16,
    },
    backButton: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    coinsHeader: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      padding: 16,
      backgroundColor: theme.colors.card,
      marginHorizontal: 20,
      marginVertical: 16,
      borderRadius: 12,
    },
    coinsText: {
      color: theme.colors.primary,
      marginLeft: 8,
    },
    tabContainer: {
      flexDirection: 'row',
      marginHorizontal: 20,
      marginBottom: 16,
      backgroundColor: theme.colors.card,
      borderRadius: 12,
      padding: 4,
    },
    tab: {
      flex: 1,
      paddingVertical: 12,
      alignItems: 'center',
      borderRadius: 8,
    },
    activeTab: {
      backgroundColor: theme.colors.primary,
    },
    tabText: {
      color: theme.colors.text,
    },
    activeTabText: {
      color: '#FFFFFF',
    },
    content: {
      flex: 1,
      paddingHorizontal: 20,
    },
    itemCard: {
      backgroundColor: theme.colors.card,
      borderRadius: 12,
      padding: 16,
      marginBottom: 12,
    },
    itemHeader: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: 12,
    },
    itemInfo: {
      flex: 1,
      marginLeft: 12,
    },
    itemTitle: {
      color: theme.colors.text,
      marginBottom: 4,
    },
    itemDescription: {
      color: theme.colors.textSecondary,
    },
    costContainer: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    costText: {
      color: theme.colors.primary,
      marginLeft: 4,
    },
    unlockButton: {
      backgroundColor: theme.colors.primary,
      borderRadius: 8,
      paddingVertical: 8,
      paddingHorizontal: 16,
      alignSelf: 'flex-start',
    },
    unlockButtonText: {
      color: '#FFFFFF',
    },
    themePreview: {
      width: 32,
      height: 32,
      borderRadius: 16,
    },
  });

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity style={styles.backButton} onPress={goBack}>
          <Icon name="arrow-back" size={24} color={theme.colors.text} />
          <TextApp preset="txt18Bold" style={styles.headerTitle}>
            Premium Features
          </TextApp>
        </TouchableOpacity>
      </View>

      <View style={styles.coinsHeader}>
        <Icon name="stars" size={24} color={theme.colors.primary} />
        <TextApp preset="txt18Bold" style={styles.coinsText}>
          {coins.amount} Coins
        </TextApp>
      </View>

      <View style={styles.tabContainer}>
        <TouchableOpacity
          style={[styles.tab, activeTab === 'features' && styles.activeTab]}
          onPress={() => setActiveTab('features')}
        >
          <TextApp
            preset="txt14Bold"
            style={[styles.tabText, activeTab === 'features' && styles.activeTabText]}
          >
            Features
          </TextApp>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.tab, activeTab === 'themes' && styles.activeTab]}
          onPress={() => setActiveTab('themes')}
        >
          <TextApp
            preset="txt14Bold"
            style={[styles.tabText, activeTab === 'themes' && styles.activeTabText]}
          >
            Themes
          </TextApp>
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {activeTab === 'features'
          ? Object.values(PREMIUM_FEATURES).map(renderFeatureItem)
          : Object.values(PREMIUM_THEMES).map(renderThemeItem)}
      </ScrollView>
    </View>
  );
};
