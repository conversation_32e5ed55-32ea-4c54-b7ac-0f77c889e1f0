import React, { useEffect, useState } from 'react';
import {
  View,
  ScrollView,
  TouchableOpacity,
  StyleSheet,
  Alert,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { useTheme } from '../../components/ThemeProvider';
import { TextApp } from '../../components';
import { usePremiumStore } from '../../store/usePremiumStore';
import { PREMIUM_THEMES } from '../../types/premium';
import { goBack } from '../../navigators/navigation-services';
import { usePremiumThemeStore } from '../../store/usePremiumThemeStore';

export const PremiumFeaturesScreen: React.FC = () => {
  const { theme } = useTheme();
  const { coins, actions } = usePremiumStore();
  const { unlockedThemes, currentTheme, actions: themeActions } = usePremiumThemeStore();

  useEffect(() => {
    // Load unlocked themes when component mounts
    themeActions.loadUnlockedThemes();
  }, []);

  const handleUnlockTheme = (themeId: string, cost: number, name: string) => {
    if (unlockedThemes.includes(themeId)) {
      // Theme already unlocked, just apply it
      themeActions.setCurrentTheme(themeId);
      Alert.alert('Theme Applied!', `${name} theme is now active.`);
      return;
    }

    if (coins.amount < cost) {
      Alert.alert(
        'Insufficient Coins',
        `You need ${cost} coins to unlock ${name}. You have ${coins.amount} coins.`,
        [{ text: 'OK' }]
      );
      return;
    }

    Alert.alert(
      'Unlock Theme',
      `Unlock ${name} theme for ${cost} coins?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Unlock',
          onPress: () => {
            const success = actions.spendCoins(cost);
            if (success) {
              themeActions.unlockTheme(themeId);
              themeActions.setCurrentTheme(themeId);
              Alert.alert('Success!', `${name} theme has been unlocked and applied!`);
            }
          },
        },
      ]
    );
  };

  const isThemeUnlocked = (themeId: string) => unlockedThemes.includes(themeId);
  const isCurrentTheme = (themeId: string) => currentTheme === themeId;

  const renderThemeItem = (themeItem: any) => {
    const unlocked = isThemeUnlocked(themeItem.id);
    const isCurrent = isCurrentTheme(themeItem.id);

    return (
      <View key={themeItem.id} style={[
        styles.itemCard,
        isCurrent && styles.currentThemeCard
      ]}>
        <View style={styles.itemHeader}>
          <View style={[styles.themePreview, { backgroundColor: themeItem.colors.primary }]} />
          <View style={styles.itemInfo}>
            <View style={styles.titleRow}>
              <TextApp preset="txt16Bold" style={styles.itemTitle}>
                {themeItem.name}
              </TextApp>
              {isCurrent && (
                <View style={styles.currentBadge}>
                  <TextApp style={styles.currentBadgeText}>Current</TextApp>
                </View>
              )}
            </View>
            <TextApp style={styles.itemDescription}>
              {themeItem.description}
            </TextApp>
          </View>
          {!unlocked && (
            <View style={styles.costContainer}>
              <Icon name="stars" size={16} color={theme.colors.primary} />
              <TextApp preset="txt14Bold" style={styles.costText}>
                {themeItem.cost}
              </TextApp>
            </View>
          )}
        </View>
        <TouchableOpacity
          style={[
            styles.unlockButton,
            unlocked && styles.applyButton,
            isCurrent && styles.currentButton
          ]}
          onPress={() => handleUnlockTheme(themeItem.id, themeItem.cost, themeItem.name)}
        >
          <TextApp preset="txt14Bold" style={[
            styles.unlockButtonText,
            isCurrent && styles.currentButtonText
          ]}>
            {isCurrent ? 'Active' : unlocked ? 'Apply' : 'Unlock'}
          </TextApp>
        </TouchableOpacity>
      </View>
    );
  };

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    header: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: 20,
      paddingVertical: 16,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.border,
    },
    headerTitle: {
      color: theme.colors.text,
      marginLeft: 16,
    },
    backButton: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    coinsHeader: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      padding: 16,
      backgroundColor: theme.colors.card,
      marginHorizontal: 20,
      marginVertical: 16,
      borderRadius: 12,
    },
    coinsText: {
      color: theme.colors.primary,
      marginLeft: 8,
    },
    tabContainer: {
      flexDirection: 'row',
      marginHorizontal: 20,
      marginBottom: 16,
      backgroundColor: theme.colors.card,
      borderRadius: 12,
      padding: 4,
    },
    tab: {
      flex: 1,
      paddingVertical: 12,
      alignItems: 'center',
      borderRadius: 8,
    },
    activeTab: {
      backgroundColor: theme.colors.primary,
    },
    tabText: {
      color: theme.colors.text,
    },
    activeTabText: {
      color: '#FFFFFF',
    },
    content: {
      flex: 1,
      paddingHorizontal: 20,
    },
    itemCard: {
      backgroundColor: theme.colors.card,
      borderRadius: 12,
      padding: 16,
      marginBottom: 12,
    },
    itemHeader: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: 12,
    },
    itemInfo: {
      flex: 1,
      marginLeft: 12,
    },
    itemTitle: {
      color: theme.colors.text,
      marginBottom: 4,
    },
    itemDescription: {
      color: theme.colors.textSecondary,
    },
    costContainer: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    costText: {
      color: theme.colors.primary,
      marginLeft: 4,
    },
    unlockButton: {
      backgroundColor: theme.colors.primary,
      borderRadius: 8,
      paddingVertical: 8,
      paddingHorizontal: 16,
      alignSelf: 'flex-start',
    },
    unlockButtonText: {
      color: '#FFFFFF',
    },
    themePreview: {
      width: 32,
      height: 32,
      borderRadius: 16,
    },
    currentThemeCard: {
      borderWidth: 2,
      borderColor: theme.colors.primary,
    },
    titleRow: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    currentBadge: {
      backgroundColor: theme.colors.primary,
      paddingHorizontal: 8,
      paddingVertical: 2,
      borderRadius: 10,
      marginLeft: 8,
    },
    currentBadgeText: {
      color: '#FFFFFF',
      fontSize: 10,
      fontWeight: '600',
    },
    applyButton: {
      backgroundColor: theme.colors.primary,
    },
    currentButton: {
      backgroundColor: theme.colors.textSecondary,
    },
    currentButtonText: {
      color: '#FFFFFF',
    },
  });

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity style={styles.backButton} onPress={goBack}>
          <Icon name="arrow-back" size={24} color={theme.colors.text} />
          <TextApp preset="txt18Bold" style={styles.headerTitle}>
            Premium Themes
          </TextApp>
        </TouchableOpacity>
      </View>

      <View style={styles.coinsHeader}>
        <Icon name="stars" size={24} color={theme.colors.primary} />
        <TextApp preset="txt18Bold" style={styles.coinsText}>
          {coins.amount} Coins
        </TextApp>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {Object.values(PREMIUM_THEMES).map(renderThemeItem)}
      </ScrollView>
    </View>
  );
};
