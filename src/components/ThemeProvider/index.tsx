import React, {createContext, useContext, useEffect} from 'react';
import {StatusBar} from 'react-native';
import {Theme} from '../../types/theme';
import {useThemeStore} from '../../store/useThemeStore';
import {usePremiumThemeStore} from '../../store/usePremiumThemeStore';
import {PREMIUM_THEMES} from '../../types/premium';

interface ThemeContextType {
  theme: Theme;
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

export const useTheme = (): ThemeContextType => {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};

interface ThemeProviderProps {
  children: React.ReactNode;
}

export const ThemeProvider: React.FC<ThemeProviderProps> = ({children}) => {
  const {theme, actions} = useThemeStore();
  const {currentTheme, actions: themeActions} = usePremiumThemeStore();

  useEffect(() => {
    // Initialize theme from storage
    actions.initializeTheme();
    themeActions.loadUnlockedThemes();
  }, [actions, themeActions]);

  // Get the current theme (premium or default)
  const getCurrentTheme = (): Theme => {
    if (currentTheme && PREMIUM_THEMES[currentTheme]) {
      const premiumTheme = PREMIUM_THEMES[currentTheme];
      return {
        ...theme,
        colors: premiumTheme.colors,
      };
    }
    return theme;
  };

  const currentActiveTheme = getCurrentTheme();

  // Debug log
  useEffect(() => {
    console.log('Current theme changed:', currentTheme);
    console.log('Active theme colors:', currentActiveTheme.colors.primary);
  }, [currentTheme, currentActiveTheme]);

  return (
    <ThemeContext.Provider value={{theme: currentActiveTheme}}>
      <StatusBar
        barStyle={currentActiveTheme.mode === 'light' ? 'dark-content' : 'light-content'}
        backgroundColor={currentActiveTheme.colors.background}
      />
      {children}
    </ThemeContext.Provider>
  );
};
